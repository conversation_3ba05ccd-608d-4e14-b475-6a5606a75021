import time
from pathlib import Path
from Ccxt_Kraken import fetch_and_store, fetch_trades
from File_Fetching import MultiTicker_Fetch_n_Store


class KrakenDataFetcher:
    def __init__(
        self,
        symbols=None,
        data_dir="kraken_data",
        fetch_interval=10,
        time_offset=1,
        ram_limit=1e6,
    ):
        """
        Initialize the Kraken data fetcher.

        Args:
            symbols (list): List of trading pairs to fetch
            data_dir (str): Directory to save data
            fetch_interval (int): Time interval between fetches in seconds
            time_offset (int): Buffer time in seconds
            ram_limit (int): RAM limit in bytes
        """
        self.symbols = symbols or [
            "BTC/USD",
            "ETH/USD",
            "XRP/USD",
            "SOL/USD",
            "DOGE/USD",
            "ADA/USD",
            "TRX/USD",
            "SUI/USD",
            "LINK/USD",
            "AVAX/USD",
            "XLM/USD",
        ]
        self.data_dir = Path(data_dir)
        self.fetch_interval = fetch_interval
        self.time_offset = time_offset
        self.ram_limit = ram_limit

        # Create data directory
        self.data_dir.mkdir(exist_ok=True)

        # Set up destination paths for both orderbook and trades
        self.orderbook_destinations = [
            str(self.data_dir / f"{symbol.replace('/', '_')}_orderbook.feather")
            for symbol in self.symbols
        ]
        self.trades_destinations = [
            str(self.data_dir / f"{symbol.replace('/', '_')}_trades.feather")
            for symbol in self.symbols
        ]

    def fetch_both(self, ticker):
        """Fetch both orderbook and trades data"""
        orderbook_data = fetch_and_store(ticker)
        trades_data = fetch_trades(ticker)
        return orderbook_data, trades_data

    def start_fetching(self, duration_minutes=60):
        """
        Start fetching data for all trading pairs.

        Args:
            duration_minutes (int): How long to run the fetcher in minutes
        """
        print(f"Starting to fetch data for {self.symbols}")
        print(f"Will fetch every {self.fetch_interval} seconds")
        print(f"Data will be saved to: {self.data_dir}")
        print(f"Will run for {duration_minutes} minutes")

        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)

        try:
            while time.time() < end_time:
                # Fetch both orderbook and trades data
                MultiTicker_Fetch_n_Store(
                    tickers=self.symbols,
                    fetch_time_interval=self.fetch_interval,
                    time_offset=self.time_offset,
                    ram_limit=self.ram_limit,
                    destinations=self.orderbook_destinations,  # We'll use orderbook destinations as base
                    fetch_function=self.fetch_both,  # This will return both orderbook and trades
                )

                # Check if we've reached the time limit
                if time.time() >= end_time:
                    break
        except KeyboardInterrupt:
            print("\nStopping data collection...")
        finally:
            print(f"\nData collection completed. Duration: {duration_minutes} minutes")


def main():
    # Create and start the fetcher
    fetcher = KrakenDataFetcher()
    fetcher.start_fetching(duration_minutes=10)


if __name__ == "__main__":
    main()
