#!/usr/bin/env python3
"""
Debug script to help identify issues with side_prediction_analysis.py
"""

import os
import sys
import glob
from pathlib import Path

def check_environment():
    """Check the basic environment setup"""
    print("=== ENVIRONMENT CHECK ===")
    print(f"Python version: {sys.version}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    
    # Check if required modules can be imported
    required_modules = ['pandas', 'numpy', 'sklearn', 'matplotlib', 'seaborn']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} imported successfully")
        except ImportError as e:
            print(f"✗ {module} import failed: {e}")
    print()

def check_data_files():
    """Check if data files exist and are accessible"""
    print("=== DATA FILES CHECK ===")
    
    data_dir = "Data_Fetching/Richard_data"
    data_path = Path(data_dir)
    
    print(f"Data directory: {data_dir}")
    print(f"Data path exists: {data_path.exists()}")
    
    if not data_path.exists():
        print(f"✗ Data directory does not exist!")
        return False
    
    # Check for trade files
    trade_files = glob.glob(str(data_path / "*_trades_*.feather"))
    print(f"Found {len(trade_files)} trade files")
    
    if len(trade_files) == 0:
        print("✗ No trade files found!")
        # Show what files do exist
        all_files = list(data_path.glob("*.feather"))
        print(f"All .feather files in directory: {len(all_files)}")
        for file in all_files[:5]:
            print(f"  {file.name}")
        return False
    
    # Extract trading pairs
    pairs = set()
    for file in trade_files:
        filename = Path(file).name
        pair = "_".join(filename.split("_")[:2])
        pairs.add(pair)
    
    pairs = sorted(list(pairs))
    print(f"✓ Found {len(pairs)} trading pairs: {pairs}")
    print()
    return True

def test_data_loading():
    """Test loading data for one trading pair"""
    print("=== DATA LOADING TEST ===")
    
    try:
        # Import the functions we need
        sys.path.append('.')
        from modeling.side_prediction_analysis import get_trading_pairs, load_trading_pair_data
        
        pairs = get_trading_pairs()
        print(f"get_trading_pairs() returned: {len(pairs)} pairs")
        
        if len(pairs) == 0:
            print("✗ No trading pairs found by get_trading_pairs()")
            return False
        
        # Try to load data for the first pair
        test_pair = pairs[0]
        print(f"Testing data loading for: {test_pair}")
        
        df = load_trading_pair_data(test_pair)
        print(f"✓ Successfully loaded {len(df)} trades")
        print(f"Columns: {df.columns.tolist()}")
        print(f"Data types: {df.dtypes.to_dict()}")
        
        # Check for required columns
        required_cols = ['Price', 'Size', 'Time', 'Side']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"✗ Missing required columns: {missing_cols}")
            return False
        
        print("✓ All required columns present")
        print()
        return True
        
    except Exception as e:
        print(f"✗ Error during data loading test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_creation():
    """Test the feature creation process"""
    print("=== FEATURE CREATION TEST ===")
    
    try:
        sys.path.append('.')
        from modeling.side_prediction_analysis import get_trading_pairs, load_trading_pair_data, create_orderbook_features
        
        pairs = get_trading_pairs()
        if len(pairs) == 0:
            print("✗ No pairs available for testing")
            return False
        
        test_pair = pairs[0]
        print(f"Testing feature creation for: {test_pair}")
        
        # Load a small sample
        df = load_trading_pair_data(test_pair)
        df_sample = df.head(1000)  # Use only first 1000 rows for testing
        print(f"Using sample of {len(df_sample)} trades")
        
        # Create features
        df_features = create_orderbook_features(df_sample)
        print(f"✓ Created features for {len(df_features)} samples")
        print(f"Number of feature columns: {len(df_features.columns)}")
        
        # Check for NaN values
        nan_counts = df_features.isnull().sum()
        total_nans = nan_counts.sum()
        print(f"Total NaN values: {total_nans}")
        
        if total_nans > len(df_features) * 0.5:  # More than 50% NaN
            print("⚠ Warning: High number of NaN values")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Error during feature creation test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all diagnostic tests"""
    print("SIDE PREDICTION ANALYSIS DIAGNOSTIC")
    print("=" * 50)
    
    # Run all checks
    env_ok = True
    try:
        check_environment()
    except Exception as e:
        print(f"Environment check failed: {e}")
        env_ok = False
    
    data_ok = False
    try:
        data_ok = check_data_files()
    except Exception as e:
        print(f"Data files check failed: {e}")
    
    loading_ok = False
    if data_ok:
        try:
            loading_ok = test_data_loading()
        except Exception as e:
            print(f"Data loading test failed: {e}")
    
    features_ok = False
    if loading_ok:
        try:
            features_ok = test_feature_creation()
        except Exception as e:
            print(f"Feature creation test failed: {e}")
    
    # Summary
    print("=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    print(f"Environment: {'✓ OK' if env_ok else '✗ FAILED'}")
    print(f"Data Files: {'✓ OK' if data_ok else '✗ FAILED'}")
    print(f"Data Loading: {'✓ OK' if loading_ok else '✗ FAILED'}")
    print(f"Feature Creation: {'✓ OK' if features_ok else '✗ FAILED'}")
    
    if all([env_ok, data_ok, loading_ok, features_ok]):
        print("\n🎉 All tests passed! The side_prediction_analysis.py should work.")
        print("If you're still having issues, please share the exact error message you're seeing.")
    else:
        print("\n❌ Some tests failed. Please address the issues above.")
        print("This explains why side_prediction_analysis.py is not working for you.")

if __name__ == "__main__":
    main()
