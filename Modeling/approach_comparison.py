"""
Comparison between Feature Engineering vs Pure Time Series approaches
for predicting <PERSON> and <PERSON><PERSON> in the next 10 seconds
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from future_trade_prediction import analyze_trading_pair_future
from time_series_prediction import analyze_time_series_prediction
import time


def compare_approaches(pair="BTC_USD", sample_size=10000):
    """
    Compare feature engineering vs pure time series approaches
    """
    print(f"🔬 COMPARING PREDICTION APPROACHES FOR {pair}")
    print("="*60)
    
    results = {}
    
    # 1. Feature Engineering Approach
    print(f"\n📊 APPROACH 1: FEATURE ENGINEERING")
    print("-" * 40)
    start_time = time.time()
    
    feature_result = analyze_trading_pair_future(
        pair=pair,
        sample_size=sample_size,
        prediction_window=10
    )
    
    feature_time = time.time() - start_time
    
    if feature_result:
        models = feature_result["model_results"]
        results["feature_engineering"] = {
            "trade_accuracy": models["trade_classifier"]["accuracy"],
            "side_accuracy": models["side_classifier"]["accuracy"],
            "size_r2": models["size_regressor"]["r2"],
            "training_time": feature_time,
            "num_features": feature_result["num_features"],
            "approach": "Feature Engineering (80+ features)"
        }
        print(f"✓ Trade Occurrence: {models['trade_classifier']['accuracy']:.1%}")
        print(f"✓ Side Prediction: {models['side_classifier']['accuracy']:.1%}")
        print(f"✓ Size Prediction R²: {models['size_regressor']['r2']:.3f}")
        print(f"✓ Training Time: {feature_time:.1f}s")
        print(f"✓ Features Used: {feature_result['num_features']}")
    else:
        print("❌ Failed")
    
    # 2. Pure Time Series Approach
    print(f"\n🕒 APPROACH 2: PURE TIME SERIES")
    print("-" * 40)
    start_time = time.time()
    
    ts_result = analyze_time_series_prediction(
        pair=pair,
        sample_size=sample_size,
        sequence_length=30,
        prediction_window=10
    )
    
    ts_time = time.time() - start_time
    
    if ts_result:
        models = ts_result["models"]
        results["time_series"] = {
            "trade_accuracy": models["trade_model"]["accuracy"],
            "side_accuracy": models["side_model"]["accuracy"],
            "size_r2": models["size_model"]["r2"],
            "training_time": ts_time,
            "sequence_length": ts_result["sequence_length"],
            "approach": "Time Series (raw sequences)"
        }
        print(f"✓ Trade Occurrence: {models['trade_model']['accuracy']:.1%}")
        print(f"✓ Side Prediction: {models['side_model']['accuracy']:.1%}")
        print(f"✓ Size Prediction R²: {models['size_model']['r2']:.3f}")
        print(f"✓ Training Time: {ts_time:.1f}s")
        print(f"✓ Sequence Length: {ts_result['sequence_length']} trades")
    else:
        print("❌ Failed")
    
    return results


def visualize_comparison(results):
    """Create comparison visualizations"""
    
    if len(results) < 2:
        print("Need both approaches to compare")
        return
    
    # Prepare data
    approaches = list(results.keys())
    metrics = ["trade_accuracy", "side_accuracy", "size_r2"]
    metric_names = ["Trade Occurrence", "Side Prediction", "Size Prediction R²"]
    
    # Create comparison plot
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Performance Comparison
    ax1 = axes[0, 0]
    x = np.arange(len(metric_names))
    width = 0.35
    
    feature_scores = [results["feature_engineering"][m] for m in metrics]
    ts_scores = [results["time_series"][m] for m in metrics]
    
    bars1 = ax1.bar(x - width/2, feature_scores, width, label='Feature Engineering', color='blue', alpha=0.7)
    bars2 = ax1.bar(x + width/2, ts_scores, width, label='Time Series', color='green', alpha=0.7)
    
    ax1.set_xlabel('Metrics')
    ax1.set_ylabel('Score')
    ax1.set_title('Performance Comparison')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metric_names, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=8)
    
    # 2. Training Time Comparison
    ax2 = axes[0, 1]
    times = [results["feature_engineering"]["training_time"], 
             results["time_series"]["training_time"]]
    colors = ['blue', 'green']
    
    bars = ax2.bar(approaches, times, color=colors, alpha=0.7)
    ax2.set_ylabel('Training Time (seconds)')
    ax2.set_title('Training Time Comparison')
    
    for bar, time_val in zip(bars, times):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{time_val:.1f}s', ha='center', va='bottom')
    
    # 3. Approach Summary
    ax3 = axes[1, 0]
    
    summary_text = f"""
    FEATURE ENGINEERING APPROACH:
    • Uses 80+ engineered features
    • OBI, momentum, volatility, etc.
    • More complex feature creation
    • Trade Acc: {results['feature_engineering']['trade_accuracy']:.1%}
    • Side Acc: {results['feature_engineering']['side_accuracy']:.1%}
    • Size R²: {results['feature_engineering']['size_r2']:.3f}
    
    TIME SERIES APPROACH:
    • Uses raw price/size/side sequences
    • No feature engineering needed
    • Simple and fast
    • Trade Acc: {results['time_series']['trade_accuracy']:.1%}
    • Side Acc: {results['time_series']['side_accuracy']:.1%}
    • Size R²: {results['time_series']['size_r2']:.3f}
    """
    
    ax3.text(0.05, 0.95, summary_text, transform=ax3.transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle="round", facecolor='lightblue'))
    ax3.set_title('Approach Comparison')
    ax3.axis('off')
    
    # 4. Pros and Cons
    ax4 = axes[1, 1]
    
    pros_cons_text = f"""
    FEATURE ENGINEERING:
    ✓ Pros:
      • Higher accuracy potential
      • Domain knowledge incorporation
      • Rich feature set
    ✗ Cons:
      • Complex feature creation
      • Longer training time
      • More prone to overfitting
    
    TIME SERIES:
    ✓ Pros:
      • Simple and fast
      • No feature engineering
      • Good baseline performance
      • Easy to understand
    ✗ Cons:
      • May miss complex patterns
      • Limited domain knowledge use
    """
    
    ax4.text(0.05, 0.95, pros_cons_text, transform=ax4.transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle="round", facecolor='lightgreen'))
    ax4.set_title('Pros and Cons')
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('modeling/approach_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()


def create_recommendation(results):
    """Provide recommendations based on results"""
    
    print(f"\n🎯 RECOMMENDATIONS")
    print("="*50)
    
    if len(results) < 2:
        print("Need both approaches to make recommendations")
        return
    
    fe_acc = results["feature_engineering"]["trade_accuracy"]
    ts_acc = results["time_series"]["trade_accuracy"]
    
    fe_side = results["feature_engineering"]["side_accuracy"]
    ts_side = results["time_series"]["side_accuracy"]
    
    fe_time = results["feature_engineering"]["training_time"]
    ts_time = results["time_series"]["training_time"]
    
    print(f"📊 PERFORMANCE SUMMARY:")
    print(f"   Feature Engineering: {fe_acc:.1%} trade, {fe_side:.1%} side ({fe_time:.1f}s)")
    print(f"   Time Series: {ts_acc:.1%} trade, {ts_side:.1%} side ({ts_time:.1f}s)")
    
    # Determine winner
    fe_score = (fe_acc + fe_side) / 2
    ts_score = (ts_acc + ts_side) / 2
    
    print(f"\n🏆 RECOMMENDATION:")
    
    if fe_score > ts_score + 0.05:  # 5% threshold
        print(f"   → Use FEATURE ENGINEERING approach")
        print(f"   → Significantly better accuracy ({fe_score:.1%} vs {ts_score:.1%})")
        print(f"   → Worth the extra complexity and time")
    elif ts_score > fe_score + 0.05:
        print(f"   → Use TIME SERIES approach")
        print(f"   → Better accuracy with much simpler approach")
        print(f"   → Faster and easier to implement")
    else:
        print(f"   → Both approaches perform similarly")
        print(f"   → Choose TIME SERIES for simplicity")
        print(f"   → Choose FEATURE ENGINEERING for potential improvements")
    
    print(f"\n💡 WHEN TO USE EACH:")
    print(f"   Feature Engineering:")
    print(f"   • When you need maximum accuracy")
    print(f"   • When you have domain expertise")
    print(f"   • When training time is not critical")
    print(f"   • For production systems with resources")
    
    print(f"\n   Time Series:")
    print(f"   • For quick prototyping")
    print(f"   • When simplicity is important")
    print(f"   • For real-time applications")
    print(f"   • As a baseline approach")


def main():
    """Main comparison function"""
    
    print("🔬 PREDICTION APPROACH COMPARISON")
    print("="*60)
    print("Comparing Feature Engineering vs Pure Time Series")
    print("for predicting Side and Size in next 10 seconds")
    print("="*60)
    
    # Run comparison
    results = compare_approaches("BTC_USD", sample_size=8000)
    
    if len(results) >= 2:
        # Create visualizations
        print(f"\n📊 Creating comparison visualizations...")
        visualize_comparison(results)
        
        # Save results
        comparison_data = []
        for approach, data in results.items():
            comparison_data.append({
                "approach": approach,
                "trade_accuracy": data["trade_accuracy"],
                "side_accuracy": data["side_accuracy"],
                "size_r2": data["size_r2"],
                "training_time": data["training_time"]
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv("modeling/approach_comparison_results.csv", index=False)
        print(f"Results saved to 'modeling/approach_comparison_results.csv'")
        
        # Provide recommendations
        create_recommendation(results)
        
        print(f"\n✅ COMPARISON COMPLETE!")
        print(f"Files created:")
        print(f"• modeling/approach_comparison.png")
        print(f"• modeling/approach_comparison_results.csv")
        
        return results
    else:
        print("❌ Could not complete comparison")
        return None


if __name__ == "__main__":
    results = main()
