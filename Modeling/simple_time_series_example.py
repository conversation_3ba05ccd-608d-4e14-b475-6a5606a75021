"""
Simple Time Series Prediction Example
Uses only raw price, size, and side sequences - no feature engineering!
"""

import pandas as pd
import numpy as np
from time_series_prediction import (
    analyze_time_series_prediction,
    predict_next_trade_simple,
    load_trading_pair_data
)


def simple_time_series_demo():
    """
    Demonstrate pure time series prediction without any feature engineering
    """
    print("🕒 SIMPLE TIME SERIES PREDICTION DEMO")
    print("="*50)
    print("✓ No feature engineering")
    print("✓ Only raw price, size, side sequences")
    print("✓ Simple linear models")
    print("="*50)
    
    # Train model using only time series data
    result = analyze_time_series_prediction(
        pair="BTC_USD",
        sample_size=10000,  # Small sample for fast demo
        sequence_length=20,  # Use last 20 trades
        prediction_window=10  # Predict 10 seconds ahead
    )
    
    if not result:
        print("❌ Failed to train model")
        return
    
    # Show what the model learned
    models = result["models"]
    print(f"\n📊 MODEL PERFORMANCE:")
    print(f"   Trade Occurrence: {models['trade_model']['accuracy']:.1%}")
    print(f"   Side Prediction: {models['side_model']['accuracy']:.1%}")
    print(f"   Size Prediction R²: {models['size_model']['r2']:.3f}")
    
    # Make a fresh prediction
    print(f"\n🔮 MAKING NEW PREDICTION...")
    df = load_trading_pair_data("BTC_USD", "Data_Fetching/Richard_data")
    recent_trades = df.tail(50)  # Get recent trades
    
    prediction = predict_next_trade_simple(
        models=result["models"],
        recent_trades=recent_trades,
        prediction_window=10
    )
    
    # Display results
    print(f"\n🎯 PREDICTION RESULTS:")
    print(f"   Current Price: ${prediction['current_price']:.2f}")
    print(f"   Trade Will Occur: {prediction['trade_will_occur']}")
    print(f"   Probability: {prediction['trade_probability']:.1%}")
    
    if prediction['trade_will_occur']:
        print(f"   Predicted Side: {prediction['predicted_side'].upper()}")
        print(f"   Buy Probability: {prediction['side_probabilities']['buy']:.1%}")
        print(f"   Sell Probability: {prediction['side_probabilities']['sell']:.1%}")
        print(f"   Predicted Size: {prediction['predicted_size']:.6f}")
    
    print(f"   Based on last {prediction['sequence_length']} trades")
    
    return result, prediction


def compare_sequence_lengths():
    """
    Compare different sequence lengths to see what works best
    """
    print(f"\n🔍 COMPARING DIFFERENT SEQUENCE LENGTHS")
    print("="*50)
    
    sequence_lengths = [10, 20, 30, 50]
    results = []
    
    for seq_len in sequence_lengths:
        print(f"\nTesting sequence length: {seq_len}")
        
        try:
            result = analyze_time_series_prediction(
                pair="BTC_USD",
                sample_size=8000,  # Small for speed
                sequence_length=seq_len,
                prediction_window=10
            )
            
            if result:
                models = result["models"]
                results.append({
                    "sequence_length": seq_len,
                    "trade_accuracy": models['trade_model']['accuracy'],
                    "side_accuracy": models['side_model']['accuracy'],
                    "size_r2": models['size_model']['r2']
                })
                
                print(f"   ✓ Trade: {models['trade_model']['accuracy']:.1%}, "
                      f"Side: {models['side_model']['accuracy']:.1%}, "
                      f"Size R²: {models['size_model']['r2']:.3f}")
            else:
                print(f"   ❌ Failed")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    # Show comparison
    if results:
        print(f"\n📊 SEQUENCE LENGTH COMPARISON:")
        results_df = pd.DataFrame(results)
        print(results_df.round(3))
        
        # Find best
        best_trade = results_df.loc[results_df['trade_accuracy'].idxmax()]
        best_side = results_df.loc[results_df['side_accuracy'].idxmax()]
        
        print(f"\n🏆 BEST RESULTS:")
        print(f"   Trade Occurrence: {best_trade['sequence_length']} trades "
              f"({best_trade['trade_accuracy']:.1%})")
        print(f"   Side Prediction: {best_side['sequence_length']} trades "
              f"({best_side['side_accuracy']:.1%})")
        
        return results_df
    
    return None


def real_time_prediction_example():
    """
    Example of how to use the model for real-time predictions
    """
    print(f"\n⚡ REAL-TIME PREDICTION EXAMPLE")
    print("="*40)
    
    # Train once
    print("Training model...")
    result = analyze_time_series_prediction(
        pair="BTC_USD",
        sample_size=12000,
        sequence_length=25,
        prediction_window=10
    )
    
    if not result:
        return
    
    # Load data
    df = load_trading_pair_data("BTC_USD", "Data_Fetching/Richard_data")
    
    # Simulate real-time predictions on different time points
    print(f"\nSimulating predictions at different times...")
    
    # Take 5 different time points
    sample_points = np.linspace(1000, len(df)-100, 5, dtype=int)
    
    for i, point in enumerate(sample_points):
        print(f"\n--- Prediction {i+1} ---")
        
        # Get "recent" data up to this point
        recent_data = df.iloc[point-50:point]
        
        # Make prediction
        pred = predict_next_trade_simple(
            models=result["models"],
            recent_trades=recent_data,
            prediction_window=10
        )
        
        print(f"Time: {pred['timestamp']}")
        print(f"Price: ${pred['current_price']:.2f}")
        print(f"Prediction: {pred['trade_will_occur']} ({pred['trade_probability']:.1%})")
        
        if pred['trade_will_occur']:
            print(f"Side: {pred['predicted_side']}, Size: {pred['predicted_size']:.6f}")


def minimal_example():
    """
    Minimal example showing the core concept
    """
    print(f"\n🎯 MINIMAL TIME SERIES EXAMPLE")
    print("="*35)
    
    # 1. Load data
    df = load_trading_pair_data("BTC_USD", "Data_Fetching/Richard_data")
    print(f"✓ Loaded {len(df)} trades")
    
    # 2. Train model (just using sequences of price, size, side)
    result = analyze_time_series_prediction(
        pair="BTC_USD",
        sample_size=5000,  # Very small for demo
        sequence_length=15,  # Last 15 trades
        prediction_window=10  # 10 seconds ahead
    )
    
    if result:
        print(f"✓ Trained model using sequences of 15 trades")
        
        # 3. Make prediction
        recent = df.tail(30)
        pred = predict_next_trade_simple(result["models"], recent, 10)
        
        print(f"✓ Prediction: {pred['trade_will_occur']} ({pred['trade_probability']:.1%})")
        if pred['trade_will_occur']:
            print(f"  Side: {pred['predicted_side']}, Size: {pred['predicted_size']:.6f}")
    
    print(f"\n💡 KEY INSIGHT:")
    print(f"   This approach uses ONLY the sequence of:")
    print(f"   - Recent prices (normalized)")
    print(f"   - Recent sizes (normalized)")  
    print(f"   - Recent sides (buy=1, sell=0)")
    print(f"   No other features needed!")


if __name__ == "__main__":
    print("🚀 TIME SERIES PREDICTION EXAMPLES")
    print("="*60)
    print("Pure time series approach - no feature engineering!")
    print("="*60)
    
    # Run examples
    minimal_example()
    simple_time_series_demo()
    
    # Optional: compare sequence lengths (takes longer)
    # compare_sequence_lengths()
    
    # Optional: real-time example (takes longer)
    # real_time_prediction_example()
    
    print(f"\n✨ EXAMPLES COMPLETED!")
    print(f"\nTime series approach summary:")
    print(f"✓ No feature engineering required")
    print(f"✓ Uses only raw price/size/side sequences")
    print(f"✓ Fast training and prediction")
    print(f"✓ Simple to understand and implement")
    print(f"✓ Good baseline performance")
