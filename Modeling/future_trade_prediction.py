import pandas as pd
import numpy as np
import glob
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.multioutput import MultiOutputRegressor, MultiOutputClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    classification_report,
    confusion_matrix,
    accuracy_score,
    roc_auc_score,
    mean_squared_error,
    mean_absolute_error,
    r2_score,
)
import warnings
from typing import Dict, List, Tuple
from datetime import timedelta

warnings.filterwarnings("ignore")


def load_trading_pair_data(pair: str, data_dir="Data_Fetching/Richard_data"):
    """Load and combine all trade data for a specific trading pair"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / f"{pair}_trades_*.feather"))

    if not trade_files:
        raise FileNotFoundError(f"No trade files found for {pair} in {data_dir}")

    print(f"Found {len(trade_files)} trade files for {pair}")

    # Load and combine all files
    dfs = []
    for file in trade_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])

    # Sort by time
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def create_time_based_features(
    df: pd.DataFrame, prediction_window_seconds=10
) -> pd.DataFrame:
    """Create features to predict trades in the next N seconds"""

    # Sort by time to ensure chronological order
    df = df.sort_values("Time").reset_index(drop=True)

    # Create time-based features
    df["Hour"] = df["Time"].dt.hour
    df["DayOfWeek"] = df["Time"].dt.dayofweek
    df["Minute"] = df["Time"].dt.minute
    df["Second"] = df["Time"].dt.second

    # Create price and volume features
    df["LogPrice"] = np.log(df["Price"])
    df["LogSize"] = np.log(df["Size"])

    # Create binary side indicator (1 for buy, 0 for sell)
    df["SideBinary"] = (df["Side"] == "buy").astype(int)

    # Calculate rolling features for different windows
    windows = [5, 10, 20, 50]
    for window in windows:
        # Price momentum features
        df[f"PriceMA_{window}"] = df["Price"].rolling(window=window).mean()
        df[f"PriceStd_{window}"] = df["Price"].rolling(window=window).std()
        df[f"PriceChange_{window}"] = df["Price"] - df[f"PriceMA_{window}"]
        df[f"PriceChangeRatio_{window}"] = (
            df[f"PriceChange_{window}"] / df[f"PriceMA_{window}"]
        )

        # Volume features
        df[f"VolumeMA_{window}"] = df["Size"].rolling(window=window).mean()
        df[f"VolumeStd_{window}"] = df["Size"].rolling(window=window).std()
        df[f"VolumeRatio_{window}"] = df["Size"] / df[f"VolumeMA_{window}"]

        # Order book imbalance features
        df[f"BuyRatio_{window}"] = df["SideBinary"].rolling(window=window).mean()
        df[f"SellRatio_{window}"] = 1 - df[f"BuyRatio_{window}"]
        df[f"OBI_{window}"] = (df[f"BuyRatio_{window}"] - df[f"SellRatio_{window}"]) / (
            df[f"BuyRatio_{window}"] + df[f"SellRatio_{window}"]
        )

        # Trade intensity features
        df[f"TradeCount_{window}"] = df["SideBinary"].rolling(window=window).count()
        df[f"TradeFreq_{window}"] = df[f"TradeCount_{window}"] / window

        # Price volatility
        df[f"Returns_{window}"] = df["Price"].pct_change(window)
        df[f"Volatility_{window}"] = (
            df[f"Returns_{window}"].rolling(window=window).std()
        )

    # Create lag features (recent trade characteristics)
    for lag in [1, 2, 3, 5, 10]:
        df[f"PriceLag_{lag}"] = df["Price"].shift(lag)
        df[f"SizeLag_{lag}"] = df["Size"].shift(lag)
        df[f"SideLag_{lag}"] = df["SideBinary"].shift(lag)
        df[f"TimeDiffLag_{lag}"] = (
            df["Time"] - df["Time"].shift(lag)
        ).dt.total_seconds()

    # Create target variables for next 10 seconds
    df = create_future_targets(df, prediction_window_seconds)

    # Remove rows with NaN values
    df = df.dropna()

    return df


def create_future_targets(df: pd.DataFrame, window_seconds: int) -> pd.DataFrame:
    """Create target variables for trades occurring in the next N seconds"""

    # Initialize target columns
    df["Future_HasTrade"] = 0  # Whether any trade occurs in next N seconds
    df["Future_Side"] = -1  # Side of the next trade (0=sell, 1=buy, -1=no trade)
    df["Future_Size"] = 0.0  # Size of the next trade
    df["Future_Price"] = 0.0  # Price of the next trade
    df["Future_TradeCount"] = 0  # Number of trades in next N seconds
    df["Future_TotalVolume"] = 0.0  # Total volume in next N seconds

    for i in range(len(df)):
        current_time = df.iloc[i]["Time"]
        future_time = current_time + timedelta(seconds=window_seconds)

        # Find trades in the next N seconds
        future_mask = (df["Time"] > current_time) & (df["Time"] <= future_time)
        future_trades = df[future_mask]

        if len(future_trades) > 0:
            # There are trades in the future window
            df.loc[i, "Future_HasTrade"] = 1

            # Get the first trade in the window
            first_trade = future_trades.iloc[0]
            df.loc[i, "Future_Side"] = first_trade["SideBinary"]
            df.loc[i, "Future_Size"] = first_trade["Size"]
            df.loc[i, "Future_Price"] = first_trade["Price"]

            # Aggregate statistics for the window
            df.loc[i, "Future_TradeCount"] = len(future_trades)
            df.loc[i, "Future_TotalVolume"] = future_trades["Size"].sum()

    return df


def prepare_features_and_targets(
    df: pd.DataFrame,
) -> Tuple[pd.DataFrame, pd.DataFrame, List[str]]:
    """Prepare feature matrix and target variables"""

    # Define feature columns (exclude non-feature columns)
    exclude_cols = [
        "Time",
        "Price",
        "Size",
        "Side",
        "SideBinary",
        "LogPrice",
        "LogSize",
        "Future_HasTrade",
        "Future_Side",
        "Future_Size",
        "Future_Price",
        "Future_TradeCount",
        "Future_TotalVolume",
    ]
    feature_cols = [col for col in df.columns if col not in exclude_cols]

    X = df[feature_cols]

    # Create target matrix
    y = df[
        [
            "Future_HasTrade",
            "Future_Side",
            "Future_Size",
            "Future_TradeCount",
            "Future_TotalVolume",
        ]
    ]

    return X, y, feature_cols


def train_prediction_models(X: pd.DataFrame, y: pd.DataFrame) -> Dict:
    """Train models to predict future trades"""

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42
    )

    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    results = {}

    # 1. Predict if any trade will occur (classification)
    print("\nTraining Trade Occurrence Classifier...")
    trade_classifier = LogisticRegression(random_state=42, max_iter=1000)
    trade_classifier.fit(X_train_scaled, y_train["Future_HasTrade"])

    trade_pred = trade_classifier.predict(X_test_scaled)
    trade_pred_proba = trade_classifier.predict_proba(X_test_scaled)[:, 1]
    trade_accuracy = accuracy_score(y_test["Future_HasTrade"], trade_pred)
    trade_auc = roc_auc_score(y_test["Future_HasTrade"], trade_pred_proba)

    print(f"Trade Occurrence - Accuracy: {trade_accuracy:.4f}, AUC: {trade_auc:.4f}")

    # 2. Predict side of next trade (classification, only for trades that occur)
    print("\nTraining Side Classifier...")
    has_trade_mask = y_train["Future_HasTrade"] == 1
    if has_trade_mask.sum() > 0:
        side_classifier = LogisticRegression(random_state=42, max_iter=1000)
        side_classifier.fit(
            X_train_scaled[has_trade_mask], y_train.loc[has_trade_mask, "Future_Side"]
        )

        # Test on samples where trades actually occur
        test_has_trade_mask = y_test["Future_HasTrade"] == 1
        if test_has_trade_mask.sum() > 0:
            side_pred = side_classifier.predict(X_test_scaled[test_has_trade_mask])
            side_accuracy = accuracy_score(
                y_test.loc[test_has_trade_mask, "Future_Side"], side_pred
            )
            print(f"Side Prediction - Accuracy: {side_accuracy:.4f}")
        else:
            side_accuracy = 0.0
            print("No trades in test set to evaluate side prediction")
    else:
        side_classifier = None
        side_accuracy = 0.0
        print("No trades in training set for side prediction")

    # 3. Predict trade size (regression, only for trades that occur)
    print("\nTraining Size Regressor...")
    if has_trade_mask.sum() > 0:
        size_regressor = LinearRegression()
        size_regressor.fit(
            X_train_scaled[has_trade_mask], y_train.loc[has_trade_mask, "Future_Size"]
        )

        # Test on samples where trades actually occur
        if test_has_trade_mask.sum() > 0:
            size_pred = size_regressor.predict(X_test_scaled[test_has_trade_mask])
            size_mse = mean_squared_error(
                y_test.loc[test_has_trade_mask, "Future_Size"], size_pred
            )
            size_mae = mean_absolute_error(
                y_test.loc[test_has_trade_mask, "Future_Size"], size_pred
            )
            size_r2 = r2_score(
                y_test.loc[test_has_trade_mask, "Future_Size"], size_pred
            )
            print(
                f"Size Prediction - MSE: {size_mse:.6f}, MAE: {size_mae:.6f}, R²: {size_r2:.4f}"
            )
        else:
            size_mse = size_mae = size_r2 = 0.0
            print("No trades in test set to evaluate size prediction")
    else:
        size_regressor = None
        size_mse = size_mae = size_r2 = 0.0
        print("No trades in training set for size prediction")

    # 4. Predict trade count (regression)
    print("\nTraining Trade Count Regressor...")
    count_regressor = LinearRegression()
    count_regressor.fit(X_train_scaled, y_train["Future_TradeCount"])

    count_pred = count_regressor.predict(X_test_scaled)
    count_mse = mean_squared_error(y_test["Future_TradeCount"], count_pred)
    count_mae = mean_absolute_error(y_test["Future_TradeCount"], count_pred)
    count_r2 = r2_score(y_test["Future_TradeCount"], count_pred)

    print(
        f"Trade Count - MSE: {count_mse:.6f}, MAE: {count_mae:.6f}, R²: {count_r2:.4f}"
    )

    results = {
        "trade_classifier": {
            "model": trade_classifier,
            "accuracy": trade_accuracy,
            "auc": trade_auc,
            "predictions": trade_pred,
            "probabilities": trade_pred_proba,
        },
        "side_classifier": {"model": side_classifier, "accuracy": side_accuracy},
        "size_regressor": {
            "model": size_regressor,
            "mse": size_mse,
            "mae": size_mae,
            "r2": size_r2,
        },
        "count_regressor": {
            "model": count_regressor,
            "mse": count_mse,
            "mae": count_mae,
            "r2": count_r2,
        },
        "scaler": scaler,
        "test_data": {"X_test": X_test_scaled, "y_test": y_test},
    }

    return results


def analyze_trading_pair_future(
    pair: str,
    data_dir="Data_Fetching/Richard_data",
    sample_size=50000,
    prediction_window=10,
) -> Dict:
    """Complete analysis for predicting future trades"""
    print(f"\n{'='*60}")
    print(f"Future Trade Prediction Analysis for {pair}")
    print(f"Prediction Window: {prediction_window} seconds")
    print(f"{'='*60}")

    try:
        # Load data
        df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(df)} trades for {pair}")

        # Sample data if it's too large
        if len(df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(df)} total trades")
            df = (
                df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Create features
        print("Creating time-based features...")
        df_features = create_time_based_features(df, prediction_window)
        print(f"Created features for {len(df_features)} samples")

        # Prepare data
        X, y, feature_cols = prepare_features_and_targets(df_features)
        print(f"Using {len(feature_cols)} features")

        # Check target distributions
        trade_ratio = y["Future_HasTrade"].mean()
        print(f"Trade occurrence ratio: {trade_ratio:.3f}")

        if y["Future_HasTrade"].sum() > 0:
            side_dist = y[y["Future_HasTrade"] == 1]["Future_Side"].value_counts(
                normalize=True
            )
            print(f"Side distribution (when trades occur): {side_dist.to_dict()}")

        # Train models
        results = train_prediction_models(X, y)

        return {
            "pair": pair,
            "data_size": len(df),
            "feature_size": len(df_features),
            "num_features": len(feature_cols),
            "prediction_window": prediction_window,
            "trade_occurrence_ratio": trade_ratio,
            "model_results": results,
            "feature_names": feature_cols,
        }

    except Exception as e:
        print(f"Error analyzing {pair}: {str(e)}")
        return None


def main():
    """Main analysis function"""
    print("Future Trade Prediction Analysis")
    print("=" * 60)

    # Analyze a single pair for demonstration
    pair = "BTC_USD"  # You can change this to any available pair
    prediction_window = 10  # seconds

    result = analyze_trading_pair_future(pair, prediction_window=prediction_window)

    if result:
        print(f"\n{'='*60}")
        print("ANALYSIS COMPLETE")
        print(f"{'='*60}")
        print(f"Successfully trained models to predict:")
        print(f"1. Whether a trade will occur in next {prediction_window} seconds")
        print(f"2. Side of the next trade (buy/sell)")
        print(f"3. Size of the next trade")
        print(f"4. Number of trades in next {prediction_window} seconds")

        # Save results
        results_summary = {
            "pair": result["pair"],
            "prediction_window": result["prediction_window"],
            "trade_occurrence_accuracy": result["model_results"]["trade_classifier"][
                "accuracy"
            ],
            "trade_occurrence_auc": result["model_results"]["trade_classifier"]["auc"],
            "side_prediction_accuracy": result["model_results"]["side_classifier"][
                "accuracy"
            ],
            "size_prediction_r2": result["model_results"]["size_regressor"]["r2"],
            "count_prediction_r2": result["model_results"]["count_regressor"]["r2"],
        }

        summary_df = pd.DataFrame([results_summary])
        summary_df.to_csv("modeling/future_trade_prediction_results.csv", index=False)
        print("\nResults saved to 'modeling/future_trade_prediction_results.csv'")
        print(f"\nSummary:")
        print(summary_df.round(4))

        return result

    return None


def predict_future_trades(
    models_result: Dict, recent_data: pd.DataFrame, prediction_window: int = 10
) -> Dict:
    """Use trained models to predict future trades based on recent data"""

    # Create features for the most recent data point
    df_features = create_time_based_features(recent_data, prediction_window)
    if len(df_features) == 0:
        return {"error": "Not enough data to create features"}

    # Get the last row (most recent) for prediction
    last_row = df_features.iloc[-1:].copy()

    # Prepare features
    X, _, feature_cols = prepare_features_and_targets(df_features)
    X_last = X.iloc[-1:].copy()

    # Scale features
    scaler = models_result["model_results"]["scaler"]
    X_scaled = scaler.transform(X_last)

    predictions = {}

    # 1. Predict if trade will occur
    trade_classifier = models_result["model_results"]["trade_classifier"]["model"]
    trade_prob = trade_classifier.predict_proba(X_scaled)[0, 1]
    trade_prediction = trade_classifier.predict(X_scaled)[0]

    predictions["trade_will_occur"] = bool(trade_prediction)
    predictions["trade_probability"] = float(trade_prob)

    # 2. Predict side (only if trade is likely to occur)
    if trade_prediction == 1:
        side_classifier = models_result["model_results"]["side_classifier"]["model"]
        if side_classifier is not None:
            side_prediction = side_classifier.predict(X_scaled)[0]
            side_prob = side_classifier.predict_proba(X_scaled)[0]
            predictions["predicted_side"] = "buy" if side_prediction == 1 else "sell"
            predictions["side_probabilities"] = {
                "sell": float(side_prob[0]),
                "buy": float(side_prob[1]),
            }
        else:
            predictions["predicted_side"] = "unknown"
            predictions["side_probabilities"] = {"sell": 0.5, "buy": 0.5}

        # 3. Predict size
        size_regressor = models_result["model_results"]["size_regressor"]["model"]
        if size_regressor is not None:
            size_prediction = size_regressor.predict(X_scaled)[0]
            predictions["predicted_size"] = float(
                max(0, size_prediction)
            )  # Ensure non-negative
        else:
            predictions["predicted_size"] = 0.0
    else:
        predictions["predicted_side"] = "no_trade"
        predictions["side_probabilities"] = {"sell": 0.0, "buy": 0.0}
        predictions["predicted_size"] = 0.0

    # 4. Predict trade count
    count_regressor = models_result["model_results"]["count_regressor"]["model"]
    count_prediction = count_regressor.predict(X_scaled)[0]
    predictions["predicted_trade_count"] = int(max(0, round(count_prediction)))

    # Add metadata
    predictions["prediction_window_seconds"] = prediction_window
    predictions["timestamp"] = recent_data["Time"].iloc[-1].isoformat()
    predictions["current_price"] = float(recent_data["Price"].iloc[-1])

    return predictions


def visualize_predictions(results: Dict):
    """Create visualizations for the prediction results"""

    model_results = results["model_results"]

    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 1. Trade Occurrence ROC Curve
    ax1 = axes[0, 0]
    from sklearn.metrics import roc_curve

    y_test = model_results["test_data"]["y_test"]["Future_HasTrade"]
    y_proba = model_results["trade_classifier"]["probabilities"]
    fpr, tpr, _ = roc_curve(y_test, y_proba)

    ax1.plot(
        fpr,
        tpr,
        label=f'ROC Curve (AUC = {model_results["trade_classifier"]["auc"]:.3f})',
    )
    ax1.plot([0, 1], [0, 1], "k--", label="Random")
    ax1.set_xlabel("False Positive Rate")
    ax1.set_ylabel("True Positive Rate")
    ax1.set_title("Trade Occurrence Prediction - ROC Curve")
    ax1.legend()
    ax1.grid(True)

    # 2. Trade Count Predictions vs Actual
    ax2 = axes[0, 1]
    y_count_test = model_results["test_data"]["y_test"]["Future_TradeCount"]
    count_pred = model_results["count_regressor"]["model"].predict(
        model_results["test_data"]["X_test"]
    )

    ax2.scatter(y_count_test, count_pred, alpha=0.5)
    ax2.plot(
        [y_count_test.min(), y_count_test.max()],
        [y_count_test.min(), y_count_test.max()],
        "r--",
    )
    ax2.set_xlabel("Actual Trade Count")
    ax2.set_ylabel("Predicted Trade Count")
    ax2.set_title(
        f'Trade Count Prediction (R² = {model_results["count_regressor"]["r2"]:.3f})'
    )
    ax2.grid(True)

    # 3. Feature Importance (Trade Occurrence)
    ax3 = axes[1, 0]
    trade_model = model_results["trade_classifier"]["model"]
    feature_names = results["feature_names"]

    if hasattr(trade_model, "coef_"):
        importances = np.abs(trade_model.coef_[0])
        top_indices = np.argsort(importances)[-10:]  # Top 10 features

        ax3.barh(range(len(top_indices)), importances[top_indices])
        ax3.set_yticks(range(len(top_indices)))
        ax3.set_yticklabels([feature_names[i] for i in top_indices])
        ax3.set_xlabel("Feature Importance (|Coefficient|)")
        ax3.set_title("Top 10 Features for Trade Occurrence")

    # 4. Prediction Summary
    ax4 = axes[1, 1]
    metrics = [
        model_results["trade_classifier"]["accuracy"],
        model_results["side_classifier"]["accuracy"],
        model_results["size_regressor"]["r2"],
        model_results["count_regressor"]["r2"],
    ]
    metric_names = ["Trade Occur\nAccuracy", "Side\nAccuracy", "Size\nR²", "Count\nR²"]

    bars = ax4.bar(metric_names, metrics, color=["blue", "green", "orange", "red"])
    ax4.set_ylabel("Score")
    ax4.set_title("Model Performance Summary")
    ax4.set_ylim(0, 1)

    # Add value labels on bars
    for bar, metric in zip(bars, metrics):
        height = bar.get_height()
        ax4.text(
            bar.get_x() + bar.get_width() / 2.0,
            height + 0.01,
            f"{metric:.3f}",
            ha="center",
            va="bottom",
        )

    plt.tight_layout()
    plt.savefig(
        "modeling/future_trade_prediction_analysis.png", dpi=300, bbox_inches="tight"
    )
    plt.show()


def demo_real_time_prediction(
    pair: str = "BTC_USD", data_dir="Data_Fetching/Richard_data"
):
    """Demonstrate real-time prediction using the trained model"""

    print(f"\n{'='*60}")
    print("REAL-TIME PREDICTION DEMO")
    print(f"{'='*60}")

    # First train the model
    result = analyze_trading_pair_future(pair, data_dir, sample_size=30000)

    if result is None:
        print("Failed to train model")
        return

    # Load fresh data for prediction
    df = load_trading_pair_data(pair, data_dir)

    # Use the last 1000 trades as "recent data"
    recent_data = df.tail(1000).copy()

    # Make prediction
    print(
        f"\nMaking prediction for {pair} based on recent {len(recent_data)} trades..."
    )
    prediction = predict_future_trades(result, recent_data)

    print(f"\n{'='*40}")
    print("PREDICTION RESULTS")
    print(f"{'='*40}")
    print(f"Timestamp: {prediction['timestamp']}")
    print(f"Current Price: ${prediction['current_price']:.2f}")
    print(f"Prediction Window: {prediction['prediction_window_seconds']} seconds")
    print(f"\nTrade Will Occur: {prediction['trade_will_occur']}")
    print(f"Trade Probability: {prediction['trade_probability']:.3f}")

    if prediction["trade_will_occur"]:
        print(f"Predicted Side: {prediction['predicted_side']}")
        print(f"Side Probabilities: {prediction['side_probabilities']}")
        print(f"Predicted Size: {prediction['predicted_size']:.6f}")

    print(f"Predicted Trade Count: {prediction['predicted_trade_count']}")

    # Create visualizations
    visualize_predictions(result)

    return result, prediction


if __name__ == "__main__":
    # Run the demo
    results, prediction = demo_real_time_prediction()
