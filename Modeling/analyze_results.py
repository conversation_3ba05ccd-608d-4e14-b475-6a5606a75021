#!/usr/bin/env python3
"""
Analyze time series prediction results
"""

import pandas as pd
import numpy as np

def analyze_time_series_results():
    """Analyze the time series prediction results"""
    
    # Load results
    df = pd.read_csv('multi_pair_time_series_results.csv')
    
    print('🎯 TIME SERIES PREDICTION RESULTS SUMMARY')
    print('=' * 60)
    print(f'Analyzed {len(df)} trading pairs')
    print()
    
    print('📊 PERFORMANCE METRICS:')
    print(f'Average Trade Occurrence Accuracy: {df["trade_accuracy"].mean():.1%}')
    print(f'Average Side Prediction Accuracy: {df["side_accuracy"].mean():.1%}')
    print(f'Average Size Prediction R²: {df["size_r2"].mean():.3f}')
    print()
    
    print('🏆 TOP PERFORMERS (by Side Prediction Accuracy):')
    top_side = df.nlargest(5, 'side_accuracy')
    for _, row in top_side.iterrows():
        print(f'  {row["pair"]}: {row["side_accuracy"]:.1%} (Trade: {row["trade_accuracy"]:.1%})')
    print()
    
    print('🎯 TOP PERFORMERS (by Trade Occurrence Accuracy):')
    top_trade = df.nlargest(5, 'trade_accuracy')
    for _, row in top_trade.iterrows():
        print(f'  {row["pair"]}: {row["trade_accuracy"]:.1%} (Side: {row["side_accuracy"]:.1%})')
    print()
    
    print('📈 SIZE PREDICTION PERFORMANCE:')
    positive_r2 = df[df['size_r2'] > 0]
    print(f'Pairs with positive R² for size prediction: {len(positive_r2)}/{len(df)}')
    if len(positive_r2) > 0:
        best_size = df.nlargest(3, 'size_r2')
        for _, row in best_size.iterrows():
            print(f'  {row["pair"]}: R² = {row["size_r2"]:.3f}')
    print()
    
    print('📋 DETAILED RESULTS:')
    print(df.round(3).to_string(index=False))
    
    return df

if __name__ == "__main__":
    results = analyze_time_series_results()
