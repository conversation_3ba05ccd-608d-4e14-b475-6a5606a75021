#!/usr/bin/env python3
"""
Simple runner script for side_prediction_analysis.py
This script sets up the Python path correctly and runs the analysis.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path so we can import from modeling
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Now run the side prediction analysis
if __name__ == "__main__":
    print("Running Side Prediction Analysis...")
    print("=" * 50)
    
    # Change to the correct directory
    os.chdir(current_dir)
    
    # Import and run the analysis
    try:
        exec(open('modeling/side_prediction_analysis.py').read())
    except Exception as e:
        print(f"Error running analysis: {e}")
        import traceback
        traceback.print_exc()
