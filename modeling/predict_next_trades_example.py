"""
Example script showing how to use the future trade prediction system
to predict <PERSON> and Size for the next 10 seconds.
"""

import pandas as pd
import numpy as np
from future_trade_prediction import (
    analyze_trading_pair_future,
    predict_future_trades,
    load_trading_pair_data
)


def train_and_predict_example(pair="BTC_USD", prediction_window=10):
    """
    Complete example of training a model and making predictions
    """
    print(f"Training model for {pair} to predict trades in next {prediction_window} seconds...")
    
    # Step 1: Train the model
    model_result = analyze_trading_pair_future(
        pair=pair,
        data_dir="Data_Fetching/Richard_data",
        sample_size=20000,  # Use smaller sample for faster training
        prediction_window=prediction_window
    )
    
    if model_result is None:
        print("Failed to train model")
        return None
    
    print(f"\nModel trained successfully!")
    print(f"Trade occurrence accuracy: {model_result['model_results']['trade_classifier']['accuracy']:.3f}")
    print(f"Side prediction accuracy: {model_result['model_results']['side_classifier']['accuracy']:.3f}")
    print(f"Size prediction R²: {model_result['model_results']['size_regressor']['r2']:.3f}")
    
    # Step 2: Load recent data for prediction
    print(f"\nLoading recent data for prediction...")
    df = load_trading_pair_data(pair, "Data_Fetching/Richard_data")
    
    # Use last 500 trades as "recent" data
    recent_data = df.tail(500).copy()
    
    # Step 3: Make prediction
    print(f"Making prediction based on {len(recent_data)} recent trades...")
    prediction = predict_future_trades(model_result, recent_data, prediction_window)
    
    return model_result, prediction


def display_prediction_results(prediction):
    """Display prediction results in a nice format"""
    
    print(f"\n{'='*50}")
    print("PREDICTION RESULTS")
    print(f"{'='*50}")
    
    print(f"📅 Timestamp: {prediction['timestamp']}")
    print(f"💰 Current Price: ${prediction['current_price']:.2f}")
    print(f"⏱️  Prediction Window: {prediction['prediction_window_seconds']} seconds")
    
    print(f"\n🎯 TRADE OCCURRENCE:")
    print(f"   Will trade occur? {prediction['trade_will_occur']}")
    print(f"   Probability: {prediction['trade_probability']:.1%}")
    
    if prediction['trade_will_occur']:
        print(f"\n📊 TRADE DETAILS:")
        print(f"   Predicted Side: {prediction['predicted_side'].upper()}")
        print(f"   Side Probabilities:")
        print(f"     - Buy:  {prediction['side_probabilities']['buy']:.1%}")
        print(f"     - Sell: {prediction['side_probabilities']['sell']:.1%}")
        print(f"   Predicted Size: {prediction['predicted_size']:.6f}")
    
    print(f"\n📈 MARKET ACTIVITY:")
    print(f"   Expected trade count: {prediction['predicted_trade_count']}")


def predict_multiple_timeframes(pair="BTC_USD"):
    """
    Example showing predictions for different time windows
    """
    print(f"\n{'='*60}")
    print(f"MULTI-TIMEFRAME PREDICTION FOR {pair}")
    print(f"{'='*60}")
    
    timeframes = [5, 10, 15, 30]  # seconds
    
    for window in timeframes:
        print(f"\n--- Predicting for {window} seconds ---")
        
        try:
            model_result = analyze_trading_pair_future(
                pair=pair,
                sample_size=15000,  # Smaller for faster demo
                prediction_window=window
            )
            
            if model_result:
                # Load recent data
                df = load_trading_pair_data(pair, "Data_Fetching/Richard_data")
                recent_data = df.tail(300).copy()
                
                # Make prediction
                prediction = predict_future_trades(model_result, recent_data, window)
                
                # Display key results
                print(f"Trade will occur: {prediction['trade_will_occur']} "
                      f"(prob: {prediction['trade_probability']:.1%})")
                
                if prediction['trade_will_occur']:
                    print(f"Predicted: {prediction['predicted_side'].upper()} "
                          f"{prediction['predicted_size']:.6f} @ ${prediction['current_price']:.2f}")
                
                print(f"Expected trades: {prediction['predicted_trade_count']}")
            
        except Exception as e:
            print(f"Error for {window}s window: {str(e)}")


def simple_prediction_demo():
    """
    Simple demo showing basic usage
    """
    print("🚀 SIMPLE TRADE PREDICTION DEMO")
    print("="*40)
    
    # Train model and make prediction
    model_result, prediction = train_and_predict_example("BTC_USD", 10)
    
    if prediction:
        display_prediction_results(prediction)
        
        # Show model performance
        print(f"\n📊 MODEL PERFORMANCE:")
        models = model_result['model_results']
        print(f"   Trade Occurrence: {models['trade_classifier']['accuracy']:.1%} accuracy")
        print(f"   Side Prediction: {models['side_classifier']['accuracy']:.1%} accuracy")
        print(f"   Size Prediction: {models['size_regressor']['r2']:.3f} R²")
        print(f"   Count Prediction: {models['count_regressor']['r2']:.3f} R²")


def trading_strategy_example(pair="BTC_USD"):
    """
    Example of how to use predictions for a trading strategy
    """
    print(f"\n💡 TRADING STRATEGY EXAMPLE FOR {pair}")
    print("="*50)
    
    # Train model
    model_result, prediction = train_and_predict_example(pair, 10)
    
    if not prediction:
        return
    
    # Define strategy rules
    min_trade_prob = 0.7  # Only trade if >70% probability
    min_side_confidence = 0.6  # Only if side prediction >60% confident
    min_size = 0.001  # Minimum trade size
    
    print(f"\n📋 STRATEGY RULES:")
    print(f"   - Trade probability > {min_trade_prob:.0%}")
    print(f"   - Side confidence > {min_side_confidence:.0%}")
    print(f"   - Minimum size: {min_size}")
    
    # Apply strategy
    trade_prob = prediction['trade_probability']
    will_trade = prediction['trade_will_occur']
    
    if will_trade and trade_prob > min_trade_prob:
        side_probs = prediction['side_probabilities']
        max_side_prob = max(side_probs.values())
        predicted_side = prediction['predicted_side']
        predicted_size = prediction['predicted_size']
        
        if max_side_prob > min_side_confidence and predicted_size > min_size:
            print(f"\n✅ TRADE SIGNAL GENERATED!")
            print(f"   Action: {predicted_side.upper()}")
            print(f"   Size: {predicted_size:.6f}")
            print(f"   Confidence: {max_side_prob:.1%}")
            print(f"   Current Price: ${prediction['current_price']:.2f}")
        else:
            print(f"\n⚠️  Trade conditions not met:")
            if max_side_prob <= min_side_confidence:
                print(f"   - Side confidence too low: {max_side_prob:.1%}")
            if predicted_size <= min_size:
                print(f"   - Size too small: {predicted_size:.6f}")
    else:
        print(f"\n❌ NO TRADE SIGNAL:")
        if not will_trade:
            print(f"   - No trade predicted")
        if trade_prob <= min_trade_prob:
            print(f"   - Trade probability too low: {trade_prob:.1%}")


if __name__ == "__main__":
    # Run different examples
    
    print("🎯 FUTURE TRADE PREDICTION EXAMPLES")
    print("="*60)
    
    # Example 1: Simple prediction
    simple_prediction_demo()
    
    # Example 2: Trading strategy
    trading_strategy_example("BTC_USD")
    
    # Example 3: Multiple timeframes (commented out for speed)
    # predict_multiple_timeframes("BTC_USD")
    
    print(f"\n✨ Examples completed!")
    print(f"\nTo use this system:")
    print(f"1. Train a model with analyze_trading_pair_future()")
    print(f"2. Load recent data with load_trading_pair_data()")
    print(f"3. Make predictions with predict_future_trades()")
