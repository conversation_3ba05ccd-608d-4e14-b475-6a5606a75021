import pandas as pd
import numpy as np
import glob
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, mean_squared_error, r2_score
import warnings
from typing import Dict, List, Tuple
from datetime import timedelta

warnings.filterwarnings("ignore")


def load_trading_pair_data(pair: str, data_dir="../Data_Fetching/Richard_data"):
    """Load and combine all trade data for a specific trading pair"""
    data_path = Path(data_dir)
    trade_files = glob.glob(str(data_path / f"{pair}_trades_*.feather"))

    if not trade_files:
        raise FileNotFoundError(f"No trade files found for {pair} in {data_dir}")

    print(f"Found {len(trade_files)} trade files for {pair}")

    # Load and combine all files
    dfs = []
    for file in trade_files:
        df = pd.read_feather(file)
        dfs.append(df)

    combined_df = pd.concat(dfs, ignore_index=True)

    # Convert time to datetime and sort
    combined_df["Time"] = pd.to_datetime(combined_df["Time"], errors="coerce")
    combined_df = combined_df.dropna(subset=["Time"])
    combined_df = combined_df.sort_values("Time").reset_index(drop=True)

    return combined_df


def create_time_series_sequences(
    df: pd.DataFrame, sequence_length: int = 50, prediction_window_seconds: int = 10
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Create time series sequences for prediction without engineered features.
    Uses only raw Price, Size, and Side data in sequential format.
    """

    # Prepare basic data
    df = df.copy()
    df["SideBinary"] = (df["Side"] == "buy").astype(int)

    # Create sequences of raw data
    sequences = []
    targets_side = []
    targets_size = []

    print(f"Creating sequences of length {sequence_length} for {len(df)} trades...")

    for i in range(sequence_length, len(df) - 1):
        # Get sequence of past trades
        sequence_data = df.iloc[i - sequence_length : i]

        # Raw time series features: Price, Size, Side (no engineering)
        price_sequence = sequence_data["Price"].values
        size_sequence = sequence_data["Size"].values
        side_sequence = sequence_data["SideBinary"].values

        # Normalize price and size sequences to relative changes
        price_normalized = price_sequence / price_sequence[0]  # Relative to first price
        size_normalized = size_sequence / size_sequence.mean()  # Relative to mean size

        # Combine into single sequence
        sequence = np.column_stack([price_normalized, size_normalized, side_sequence])
        sequences.append(sequence.flatten())  # Flatten to 1D

        # Find target in next N seconds
        current_time = df.iloc[i]["Time"]
        future_time = current_time + timedelta(seconds=prediction_window_seconds)

        # Find next trade in the prediction window
        future_mask = (df["Time"] > current_time) & (df["Time"] <= future_time)
        future_trades = df[future_mask]

        if len(future_trades) > 0:
            # Use first trade in the window
            next_trade = future_trades.iloc[0]
            targets_side.append(next_trade["SideBinary"])
            targets_size.append(next_trade["Size"])
        else:
            # No trade in window
            targets_side.append(-1)  # Special value for "no trade"
            targets_size.append(0.0)

    sequences = np.array(sequences)
    targets_side = np.array(targets_side)
    targets_size = np.array(targets_size)

    print(f"Created {len(sequences)} sequences")
    print(f"Sequence shape: {sequences.shape}")
    print(
        f"Target distribution - No trade: {(targets_side == -1).sum()}, "
        f"Buy: {(targets_side == 1).sum()}, Sell: {(targets_side == 0).sum()}"
    )

    return sequences, targets_side, targets_size


def train_time_series_models(
    X: np.ndarray, y_side: np.ndarray, y_size: np.ndarray
) -> Dict:
    """Train simple linear models on time series sequences"""

    # Split data
    X_train, X_test, y_side_train, y_side_test, y_size_train, y_size_test = (
        train_test_split(X, y_side, y_size, test_size=0.3, random_state=42)
    )

    # Scale the sequences
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    results = {}

    # 1. Predict if trade will occur (side != -1)
    print("\nTraining Trade Occurrence Model...")
    has_trade_train = (y_side_train != -1).astype(int)
    has_trade_test = (y_side_test != -1).astype(int)

    trade_model = LogisticRegression(random_state=42, max_iter=1000)
    trade_model.fit(X_train_scaled, has_trade_train)

    trade_pred = trade_model.predict(X_test_scaled)
    trade_accuracy = accuracy_score(has_trade_test, trade_pred)

    print(f"Trade Occurrence Accuracy: {trade_accuracy:.4f}")

    # 2. Predict side (only for trades that occur)
    print("\nTraining Side Prediction Model...")
    trade_mask_train = y_side_train != -1
    trade_mask_test = y_side_test != -1

    if trade_mask_train.sum() > 0 and trade_mask_test.sum() > 0:
        side_model = LogisticRegression(random_state=42, max_iter=1000)
        side_model.fit(X_train_scaled[trade_mask_train], y_side_train[trade_mask_train])

        side_pred = side_model.predict(X_test_scaled[trade_mask_test])
        side_accuracy = accuracy_score(y_side_test[trade_mask_test], side_pred)

        print(f"Side Prediction Accuracy: {side_accuracy:.4f}")
    else:
        side_model = None
        side_accuracy = 0.0
        print("Not enough trades for side prediction")

    # 3. Predict size (only for trades that occur)
    print("\nTraining Size Prediction Model...")
    if trade_mask_train.sum() > 0 and trade_mask_test.sum() > 0:
        size_model = LinearRegression()
        size_model.fit(X_train_scaled[trade_mask_train], y_size_train[trade_mask_train])

        size_pred = size_model.predict(X_test_scaled[trade_mask_test])
        size_mse = mean_squared_error(y_size_test[trade_mask_test], size_pred)
        size_r2 = r2_score(y_size_test[trade_mask_test], size_pred)

        print(f"Size Prediction - MSE: {size_mse:.6f}, R²: {size_r2:.4f}")
    else:
        size_model = None
        size_mse = size_r2 = 0.0
        print("Not enough trades for size prediction")

    results = {
        "trade_model": {
            "model": trade_model,
            "accuracy": trade_accuracy,
            "test_predictions": trade_pred,
            "test_actual": has_trade_test,
        },
        "side_model": {"model": side_model, "accuracy": side_accuracy},
        "size_model": {"model": size_model, "mse": size_mse, "r2": size_r2},
        "scaler": scaler,
        "sequence_length": X.shape[1]
        // 3,  # Divided by 3 because we have price, size, side
    }

    return results


def predict_next_trade_simple(
    models: Dict, recent_trades: pd.DataFrame, prediction_window: int = 10
) -> Dict:
    """Make predictions using only time series data"""

    sequence_length = models["sequence_length"]

    if len(recent_trades) < sequence_length:
        return {"error": f"Need at least {sequence_length} recent trades"}

    # Prepare the most recent sequence
    recent_trades = recent_trades.copy()
    recent_trades["SideBinary"] = (recent_trades["Side"] == "buy").astype(int)

    # Get last sequence
    last_sequence = recent_trades.tail(sequence_length)

    # Create sequence (same format as training)
    price_sequence = last_sequence["Price"].values
    size_sequence = last_sequence["Size"].values
    side_sequence = last_sequence["SideBinary"].values

    # Normalize
    price_normalized = price_sequence / price_sequence[0]
    size_normalized = size_sequence / size_sequence.mean()

    # Combine and flatten
    sequence = np.column_stack([price_normalized, size_normalized, side_sequence])
    X = sequence.flatten().reshape(1, -1)

    # Scale
    scaler = models["scaler"]
    X_scaled = scaler.transform(X)

    # Make predictions
    predictions = {}

    # 1. Trade occurrence
    trade_model = models["trade_model"]["model"]
    trade_prob = trade_model.predict_proba(X_scaled)[0, 1]
    trade_pred = trade_model.predict(X_scaled)[0]

    predictions["trade_will_occur"] = bool(trade_pred)
    predictions["trade_probability"] = float(trade_prob)

    # 2. Side prediction
    if trade_pred == 1 and models["side_model"]["model"] is not None:
        side_model = models["side_model"]["model"]
        side_pred = side_model.predict(X_scaled)[0]
        side_prob = side_model.predict_proba(X_scaled)[0]

        predictions["predicted_side"] = "buy" if side_pred == 1 else "sell"
        predictions["side_probabilities"] = {
            "sell": float(side_prob[0]),
            "buy": float(side_prob[1]),
        }
    else:
        predictions["predicted_side"] = "no_trade"
        predictions["side_probabilities"] = {"sell": 0.0, "buy": 0.0}

    # 3. Size prediction
    if trade_pred == 1 and models["size_model"]["model"] is not None:
        size_model = models["size_model"]["model"]
        size_pred = size_model.predict(X_scaled)[0]
        predictions["predicted_size"] = float(max(0, size_pred))
    else:
        predictions["predicted_size"] = 0.0

    # Add metadata
    predictions["prediction_window_seconds"] = prediction_window
    predictions["current_price"] = float(recent_trades["Price"].iloc[-1])
    predictions["timestamp"] = recent_trades["Time"].iloc[-1].isoformat()
    predictions["sequence_length"] = sequence_length

    return predictions


def analyze_time_series_prediction(
    pair: str,
    data_dir="Data_Fetching/Richard_data",
    sample_size: int = 20000,
    sequence_length: int = 50,
    prediction_window: int = 10,
) -> Dict:
    """Complete time series analysis without feature engineering"""

    print(f"\n{'='*60}")
    print(f"TIME SERIES PREDICTION ANALYSIS FOR {pair}")
    print(f"Using raw sequences without feature engineering")
    print(f"Sequence length: {sequence_length} trades")
    print(f"Prediction window: {prediction_window} seconds")
    print(f"{'='*60}")

    try:
        # Load data
        df = load_trading_pair_data(pair, data_dir)
        print(f"Loaded {len(df)} trades")

        # Sample if too large
        if len(df) > sample_size:
            print(f"Sampling {sample_size} trades from {len(df)} total")
            df = (
                df.sample(n=sample_size, random_state=42)
                .sort_values("Time")
                .reset_index(drop=True)
            )

        # Create time series sequences
        X, y_side, y_size = create_time_series_sequences(
            df, sequence_length, prediction_window
        )

        if len(X) == 0:
            print("No sequences created")
            return None

        # Train models
        models = train_time_series_models(X, y_side, y_size)

        # Test prediction on recent data
        recent_data = df.tail(sequence_length + 100)
        prediction = predict_next_trade_simple(models, recent_data, prediction_window)

        return {
            "pair": pair,
            "models": models,
            "sample_prediction": prediction,
            "data_size": len(df),
            "sequence_length": sequence_length,
            "prediction_window": prediction_window,
        }

    except Exception as e:
        print(f"Error in analysis: {str(e)}")
        return None


def visualize_time_series_results(result: Dict):
    """Create visualizations for time series results"""

    if not result:
        return

    models = result["models"]

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. Trade occurrence accuracy
    ax1 = axes[0, 0]
    trade_acc = models["trade_model"]["accuracy"]
    side_acc = models["side_model"]["accuracy"]
    size_r2 = models["size_model"]["r2"]

    metrics = [trade_acc, side_acc, size_r2]
    labels = ["Trade\nOccurrence", "Side\nPrediction", "Size\nPrediction"]
    colors = ["blue", "green", "orange"]

    bars = ax1.bar(labels, metrics, color=colors)
    ax1.set_ylabel("Score")
    ax1.set_title("Time Series Model Performance")
    ax1.set_ylim(0, 1)

    for bar, metric in zip(bars, metrics):
        height = bar.get_height()
        ax1.text(
            bar.get_x() + bar.get_width() / 2.0,
            height + 0.01,
            f"{metric:.3f}",
            ha="center",
            va="bottom",
        )

    # 2. Prediction example
    ax2 = axes[0, 1]
    pred = result["sample_prediction"]

    info_text = f"""
    Current Price: ${pred['current_price']:.2f}
    Trade Will Occur: {pred['trade_will_occur']}
    Probability: {pred['trade_probability']:.1%}
    Predicted Side: {pred['predicted_side']}
    Predicted Size: {pred['predicted_size']:.6f}
    Sequence Length: {pred['sequence_length']} trades
    """

    ax2.text(
        0.1,
        0.5,
        info_text,
        transform=ax2.transAxes,
        fontsize=10,
        verticalalignment="center",
        bbox=dict(boxstyle="round", facecolor="lightblue"),
    )
    ax2.set_title("Sample Prediction")
    ax2.axis("off")

    # 3. Trade occurrence confusion matrix
    ax3 = axes[1, 0]
    from sklearn.metrics import confusion_matrix

    y_true = models["trade_model"]["test_actual"]
    y_pred = models["trade_model"]["test_predictions"]
    cm = confusion_matrix(y_true, y_pred)

    im = ax3.imshow(cm, interpolation="nearest", cmap="Blues")
    ax3.set_title("Trade Occurrence\nConfusion Matrix")

    # Add text annotations
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            ax3.text(j, i, str(cm[i, j]), ha="center", va="center")

    ax3.set_xlabel("Predicted")
    ax3.set_ylabel("Actual")
    ax3.set_xticks([0, 1])
    ax3.set_yticks([0, 1])
    ax3.set_xticklabels(["No Trade", "Trade"])
    ax3.set_yticklabels(["No Trade", "Trade"])

    # 4. Model comparison
    ax4 = axes[1, 1]
    comparison_text = f"""
    TIME SERIES APPROACH:
    ✓ No feature engineering
    ✓ Uses raw price/size/side sequences
    ✓ Simple linear models
    ✓ Fast training and prediction
    
    RESULTS:
    • Trade Occurrence: {trade_acc:.1%}
    • Side Prediction: {side_acc:.1%}
    • Size Prediction R²: {size_r2:.3f}
    
    SEQUENCE LENGTH: {result['sequence_length']} trades
    PREDICTION WINDOW: {result['prediction_window']}s
    """

    ax4.text(
        0.05,
        0.95,
        comparison_text,
        transform=ax4.transAxes,
        fontsize=9,
        verticalalignment="top",
        bbox=dict(boxstyle="round", facecolor="lightgreen"),
    )
    ax4.set_title("Time Series Approach Summary")
    ax4.axis("off")

    plt.tight_layout()
    plt.savefig(
        "modeling/time_series_prediction_results.png", dpi=300, bbox_inches="tight"
    )
    plt.show()


def main():
    """Main function to run time series analysis"""

    print("🕒 TIME SERIES PREDICTION (NO FEATURE ENGINEERING)")
    print("=" * 60)

    # Analyze BTC_USD with time series approach
    result = analyze_time_series_prediction(
        pair="BTC_USD",
        sample_size=15000,  # Smaller for faster demo
        sequence_length=30,  # 30 recent trades
        prediction_window=10,  # 10 seconds ahead
    )

    if result:
        print(f"\n✅ ANALYSIS COMPLETE")
        print(
            f"Trade Occurrence Accuracy: {result['models']['trade_model']['accuracy']:.1%}"
        )
        print(
            f"Side Prediction Accuracy: {result['models']['side_model']['accuracy']:.1%}"
        )
        print(f"Size Prediction R²: {result['models']['size_model']['r2']:.3f}")

        # Show sample prediction
        pred = result["sample_prediction"]
        print(f"\n🎯 SAMPLE PREDICTION:")
        print(
            f"Trade will occur: {pred['trade_will_occur']} ({pred['trade_probability']:.1%})"
        )
        if pred["trade_will_occur"]:
            print(f"Side: {pred['predicted_side']}, Size: {pred['predicted_size']:.6f}")

        # Create visualizations
        visualize_time_series_results(result)

        # Save summary
        summary = {
            "approach": "time_series",
            "pair": result["pair"],
            "sequence_length": result["sequence_length"],
            "prediction_window": result["prediction_window"],
            "trade_accuracy": result["models"]["trade_model"]["accuracy"],
            "side_accuracy": result["models"]["side_model"]["accuracy"],
            "size_r2": result["models"]["size_model"]["r2"],
        }

        summary_df = pd.DataFrame([summary])
        summary_df.to_csv("modeling/time_series_prediction_summary.csv", index=False)
        print(f"\nResults saved to 'modeling/time_series_prediction_summary.csv'")

        return result

    return None


if __name__ == "__main__":
    results = main()
